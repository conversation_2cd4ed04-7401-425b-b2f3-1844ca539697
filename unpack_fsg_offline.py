#!/usr/bin/env python3
"""
FSG Unpacker using Unipacker
Offline version that works with local unipacker installation
"""

import sys
import os
import subprocess
from pathlib import Path

def setup_unipacker_path():
    """Add unipacker to Python path"""
    unipacker_path = Path("unipacker-1.0.8")
    if unipacker_path.exists():
        sys.path.insert(0, str(unipacker_path))
        print(f"[+] Added {unipacker_path} to Python path")
        return True
    else:
        print(f"[-] Unipacker directory not found: {unipacker_path}")
        return False

def test_unipacker_import():
    """Test if unipacker can be imported"""
    try:
        import unipacker
        print(f"[+] Unipacker imported successfully, version: {unipacker.__VERSION__}")
        return True
    except ImportError as e:
        print(f"[-] Failed to import unipacker: {e}")
        return False

def unpack_with_unipacker(file_path, output_dir="unpacked_output"):
    """Unpack file using unipacker"""
    try:
        # Import unipacker modules
        from unipacker.core import Sample, UnpackerEngine
        from unipacker.io_handler import IOHandler

        print(f"[+] Starting unpacking of {file_path}")

        # Create output directory
        os.makedirs(output_dir, exist_ok=True)

        # Create Sample object
        print("[+] Creating Sample object...")
        sample = Sample(file_path)
        print(f"[+] Sample created: {sample}")
        print(f"[+] Detected packer: {sample.unpacker.name}")

        # Method 1: Use IOHandler (recommended)
        print("[+] Using IOHandler for unpacking...")
        samples = [sample]
        io_handler = IOHandler(samples, output_dir, partition_by_packer=False)

        print("[+] Unpacking completed!")

        # Check for output files
        output_path = Path(output_dir)
        if output_path.exists():
            output_files = list(output_path.glob("*"))
            if output_files:
                print(f"[+] Found {len(output_files)} output files:")
                for file in output_files:
                    print(f"    - {file.name} ({file.stat().st_size} bytes)")
            else:
                print("[-] No output files found")

        return True

    except Exception as e:
        print(f"[-] Unpacking failed: {e}")
        print(f"[-] Error type: {type(e).__name__}")
        import traceback
        traceback.print_exc()
        return False

def run_interactive_unipacker():
    """Run unipacker in interactive mode"""
    try:
        print("[+] Starting interactive unipacker shell...")
        print("[!] Use these commands:")
        print("    load GSEEGB.exe")
        print("    unpack")
        print("    quit")
        
        # Try to run the shell
        from unipacker.shell import main
        main()
        
    except Exception as e:
        print(f"[-] Failed to start interactive shell: {e}")
        
        # Fallback: try command line
        try:
            print("[+] Trying command line unipacker...")
            subprocess.run([sys.executable, "-m", "unipacker.shell"], check=True)
        except Exception as e2:
            print(f"[-] Command line also failed: {e2}")

def main():
    print("=" * 50)
    print("FSG Unpacker using Unipacker (Offline Version)")
    print("=" * 50)
    
    target_file = "GSEEGB.exe"
    
    # Check if target file exists
    if not os.path.exists(target_file):
        print(f"[-] Target file not found: {target_file}")
        return 1
    
    print(f"[+] Target file: {target_file}")
    print(f"[+] File size: {os.path.getsize(target_file)} bytes")
    
    # Setup unipacker path
    if not setup_unipacker_path():
        print("[-] Failed to setup unipacker path")
        return 1
    
    # Test import
    if not test_unipacker_import():
        print("[-] Failed to import unipacker")
        return 1
    
    # Ask user for unpacking method
    print("\nChoose unpacking method:")
    print("1. Automatic unpacking")
    print("2. Interactive shell")
    
    try:
        choice = input("Enter choice (1 or 2): ").strip()
    except KeyboardInterrupt:
        print("\n[-] Cancelled by user")
        return 1
    
    if choice == "1":
        print("\n[+] Running automatic unpacking...")
        success = unpack_with_unipacker(target_file)
        if success:
            print("\n[+] Automatic unpacking completed!")
        else:
            print("\n[-] Automatic unpacking failed")
            print("[!] Try interactive mode or manual tools")
    
    elif choice == "2":
        print("\n[+] Running interactive mode...")
        run_interactive_unipacker()
    
    else:
        print(f"[-] Invalid choice: {choice}")
        print("[+] Running automatic unpacking as default...")
        unpack_with_unipacker(target_file)
    
    print("\n" + "=" * 50)
    print("Unpacking process completed")
    print("Check 'unpacked_output' directory for results")
    print("=" * 50)
    
    return 0

if __name__ == "__main__":
    sys.exit(main())

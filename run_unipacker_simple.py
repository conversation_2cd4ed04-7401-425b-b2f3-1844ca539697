#!/usr/bin/env python3
"""
Simple Unipacker runner for FSG files
Uses command line interface to avoid API issues
"""

import sys
import os
import subprocess
from pathlib import Path

def setup_environment():
    """Setup Python path for unipacker"""
    unipacker_path = Path("unipacker-1.0.8")
    if unipacker_path.exists():
        sys.path.insert(0, str(unipacker_path))
        print(f"[+] Added {unipacker_path} to Python path")
        return True
    else:
        print(f"[-] Unipacker directory not found: {unipacker_path}")
        return False

def run_unipacker_cmdline(target_file, output_dir="unpacked_output"):
    """Run unipacker using command line interface"""
    try:
        # Create output directory
        os.makedirs(output_dir, exist_ok=True)
        
        print(f"[+] Running unipacker on {target_file}")
        print(f"[+] Output directory: {output_dir}")
        print("[!] This may take several minutes...")
        
        # Run unipacker using the shell module
        cmd = [
            sys.executable, "-m", "unipacker.shell",
            "--dest", output_dir,
            target_file
        ]
        
        print(f"[+] Command: {' '.join(cmd)}")
        
        # Run the command
        result = subprocess.run(cmd, 
                              capture_output=True, 
                              text=True, 
                              timeout=600)  # 10 minute timeout
        
        print(f"[+] Return code: {result.returncode}")
        
        if result.stdout:
            print("[+] STDOUT:")
            print(result.stdout)
        
        if result.stderr:
            print("[+] STDERR:")
            print(result.stderr)
        
        # Check for output files
        output_path = Path(output_dir)
        if output_path.exists():
            output_files = list(output_path.glob("*"))
            if output_files:
                print(f"[+] Found {len(output_files)} output files:")
                for file in output_files:
                    print(f"    - {file.name} ({file.stat().st_size} bytes)")
                return True
            else:
                print("[-] No output files found")
        
        return result.returncode == 0
        
    except subprocess.TimeoutExpired:
        print("[-] Unipacker timed out after 10 minutes")
        return False
    except Exception as e:
        print(f"[-] Error running unipacker: {e}")
        return False

def run_interactive_unipacker():
    """Run unipacker in interactive mode"""
    try:
        print("[+] Starting interactive unipacker...")
        print("[!] Commands to use:")
        print("    load GSEEGB.exe")
        print("    unpack")
        print("    quit")
        print()
        
        # Run interactive shell
        cmd = [sys.executable, "-m", "unipacker.shell", "--interactive"]
        subprocess.run(cmd)
        
    except Exception as e:
        print(f"[-] Error starting interactive mode: {e}")

def try_alternative_methods(target_file):
    """Try alternative unpacking methods"""
    print("\n[+] Trying alternative methods...")
    
    # Method 1: Try with different parameters
    print("[1] Trying with partition-by-packer option...")
    try:
        cmd = [
            sys.executable, "-m", "unipacker.shell",
            "--dest", "unpacked_output",
            "--partition-by-packer",
            target_file
        ]
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)
        if result.returncode == 0:
            print("[+] Success with partition-by-packer!")
            return True
    except Exception as e:
        print(f"[-] Failed: {e}")
    
    # Method 2: Try interactive mode with auto-loading
    print("[2] Trying interactive mode...")
    try:
        run_interactive_unipacker()
        return True
    except Exception as e:
        print(f"[-] Failed: {e}")
    
    return False

def main():
    print("=" * 60)
    print("Simple Unipacker Runner for FSG Files")
    print("=" * 60)
    
    target_file = "GSEEGB.exe"
    
    # Check if target file exists
    if not os.path.exists(target_file):
        print(f"[-] Target file not found: {target_file}")
        return 1
    
    print(f"[+] Target file: {target_file}")
    print(f"[+] File size: {os.path.getsize(target_file)} bytes")
    
    # Setup environment
    if not setup_environment():
        return 1
    
    # Try command line unpacking
    print("\n[+] Attempting automatic unpacking...")
    success = run_unipacker_cmdline(target_file)
    
    if not success:
        print("\n[-] Automatic unpacking failed")
        
        # Ask user if they want to try alternatives
        try:
            choice = input("\nTry alternative methods? (y/n): ").strip().lower()
            if choice.startswith('y'):
                success = try_alternative_methods(target_file)
        except KeyboardInterrupt:
            print("\n[-] Cancelled by user")
            return 1
    
    if success:
        print("\n[+] Unpacking completed successfully!")
    else:
        print("\n[-] All unpacking methods failed")
        print("\n[!] Manual unpacking recommendations:")
        print("    1. Use x64dbg or OllyDbg for manual unpacking")
        print("    2. Try online unpacking services")
        print("    3. Use specialized FSG unpackers")
        print("    4. Analyze with IDA Pro or Ghidra")
    
    print("\n" + "=" * 60)
    print("Process completed")
    print("=" * 60)
    
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())

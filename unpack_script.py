﻿import sys
import os
sys.path.insert(0, 'unipacker-1.0.8')

from unipacker.core import Unpacker<PERSON>ngine
from unipacker.utils import print_cols

def unpack_file(file_path):
    print(f"[+] Starting unpacking of {file_path}")
    
    try:
        # Initialize unpacker engine
        engine = UnpackerEngine(file_path)
        
        # Set output directory
        engine.set_output_dir("unpacked_output")
        
        # Start unpacking
        print("[+] Starting emulation...")
        engine.start()
        
        print("[+] Unpacking completed!")
        return True
        
    except Exception as e:
        print(f"[-] Unpacking failed: {e}")
        return False

if __name__ == "__main__":
    if len(sys.argv) != 2:
        print("Usage: python unpack_script.py <file>")
        sys.exit(1)
    
    file_path = sys.argv[1]
    success = unpack_file(file_path)
    sys.exit(0 if success else 1)

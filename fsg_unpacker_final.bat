@echo off
echo ================================================================
echo                    FSG 解包工具集合
echo ================================================================
echo.
echo 目标文件: GSEEGB.exe
echo 检测结果: FSG (Fast Small Good) 打包
echo 文件大小: 7,824,384 字节
echo 入口点: 0x75C100
echo.
echo ================================================================
echo                    可用的解包方法
echo ================================================================
echo.
echo [1] 自动解包尝试
echo     - UPX 解包 (已测试 - 失败)
echo     - Unipacker 解包 (已测试 - 失败，PE头部问题)
echo.
echo [2] 手动解包工具 (推荐)
echo     - x64dbg: https://x64dbg.com/
echo     - OllyDbg: http://www.ollydbg.de/
echo     - PEiD + FSG插件: https://www.aldeid.com/wiki/PEiD
echo.
echo [3] 在线解包服务
echo     - VirusTotal: https://www.virustotal.com/
echo     - Hybrid Analysis: https://www.hybrid-analysis.com/
echo     - Any.run: https://any.run/
echo.
echo [4] 生成的辅助文件
if exist "analyze_packer.ps1" echo     ✓ analyze_packer.ps1 - PE文件分析脚本
if exist "debug_config.txt" echo     ✓ debug_config.txt - 调试器配置
if exist "fsg_python_unpacker.py" echo     ✓ fsg_python_unpacker.py - Python解包脚本
if exist "fsg_unpacking_guide.md" echo     ✓ fsg_unpacking_guide.md - 详细解包指南
if exist "run_unipacker_simple.py" echo     ✓ run_unipacker_simple.py - Unipacker运行脚本
echo.
echo ================================================================
echo                    快速操作选项
echo ================================================================
echo.
echo 选择操作:
echo [A] 运行 PE 文件分析
echo [B] 尝试 Python Unipacker
echo [C] 查看调试器配置
echo [D] 打开解包指南
echo [E] 退出
echo.
set /p choice="请输入选择 (A/B/C/D/E): "

if /i "%choice%"=="A" goto analyze
if /i "%choice%"=="B" goto unipacker
if /i "%choice%"=="C" goto config
if /i "%choice%"=="D" goto guide
if /i "%choice%"=="E" goto end
goto invalid

:analyze
echo.
echo 正在运行 PE 文件分析...
if exist "analyze_packer.ps1" (
    powershell -ExecutionPolicy Bypass -File analyze_packer.ps1 -FilePath "GSEEGB.exe"
) else (
    echo 错误: analyze_packer.ps1 文件不存在
)
goto menu

:unipacker
echo.
echo 正在尝试 Unipacker 解包...
if exist "run_unipacker_simple.py" (
    python run_unipacker_simple.py
) else (
    echo 错误: run_unipacker_simple.py 文件不存在
)
goto menu

:config
echo.
echo 调试器配置信息:
echo ================================
if exist "debug_config.txt" (
    type debug_config.txt
) else (
    echo 错误: debug_config.txt 文件不存在
)
echo ================================
goto menu

:guide
echo.
echo 正在打开解包指南...
if exist "fsg_unpacking_guide.md" (
    start notepad "fsg_unpacking_guide.md"
) else (
    echo 错误: fsg_unpacking_guide.md 文件不存在
)
goto menu

:invalid
echo.
echo 无效选择，请重新输入
goto menu

:menu
echo.
echo 按任意键返回主菜单...
pause >nul
cls
goto start

:start
echo ================================================================
echo                    FSG 解包工具集合
echo ================================================================
echo.
echo 目标文件: GSEEGB.exe
echo 检测结果: FSG (Fast Small Good) 打包
echo 文件大小: 7,824,384 字节
echo 入口点: 0x75C100
echo.
echo ================================================================
echo                    快速操作选项
echo ================================================================
echo.
echo 选择操作:
echo [A] 运行 PE 文件分析
echo [B] 尝试 Python Unipacker
echo [C] 查看调试器配置
echo [D] 打开解包指南
echo [E] 退出
echo.
set /p choice="请输入选择 (A/B/C/D/E): "

if /i "%choice%"=="A" goto analyze
if /i "%choice%"=="B" goto unipacker
if /i "%choice%"=="C" goto config
if /i "%choice%"=="D" goto guide
if /i "%choice%"=="E" goto end
goto invalid

:end
echo.
echo ================================================================
echo                        总结
echo ================================================================
echo.
echo 自动解包结果: 失败 (FSG修改了PE头部结构)
echo.
echo 推荐的下一步:
echo 1. 使用 x64dbg 进行手动解包 (最推荐)
echo 2. 尝试 OllyDbg + 手动分析
echo 3. 寻找专门的 FSG 解包工具
echo 4. 使用在线解包服务 (注意安全性)
echo.
echo 手动解包关键步骤:
echo 1. 在调试器中加载 GSEEGB.exe
echo 2. 在入口点 0x75C100 设置断点
echo 3. 单步执行找到原始入口点 (OEP)
echo 4. 在 OEP 处转储内存
echo 5. 修复导入表和重定位表
echo.
echo 详细信息请查看: fsg_unpacking_guide.md
echo.
echo 感谢使用 FSG 解包工具集合！
echo ================================================================
pause

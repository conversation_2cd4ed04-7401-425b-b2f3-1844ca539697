param(
    [string]$TargetFile = "GSEEGB.exe"
)

function Write-ColorOutput {
    param([string]$Message, [string]$Color = "White")
    Write-Host $Message -ForegroundColor $Color
}

function Install-Unipacker {
    Write-ColorOutput "=== Installing Unipacker ===" "Green"
    
    # Check Python version
    try {
        $pythonVersion = python --version 2>&1
        Write-ColorOutput "Python version: $pythonVersion" "Cyan"
        
        # Check if Python 3.10+ is available
        $versionMatch = $pythonVersion -match "Python (\d+)\.(\d+)"
        if ($versionMatch) {
            $majorVersion = [int]$matches[1]
            $minorVersion = [int]$matches[2]
            
            if ($majorVersion -lt 3 -or ($majorVersion -eq 3 -and $minorVersion -lt 10)) {
                Write-ColorOutput "Warning: Unipacker requires Python 3.10+, you have Python $majorVersion.$minorVersion" "Yellow"
                Write-ColorOutput "Please install Python 3.10 or later" "Red"
                return $false
            }
        }
    } catch {
        Write-ColorOutput "Error: Python not found. Please install Python 3.10+" "Red"
        return $false
    }
    
    # Install from local directory
    Write-ColorOutput "Installing unipacker from local directory..." "Yellow"
    try {
        Set-Location "unipacker-1.0.8"
        
        # Install dependencies first
        Write-ColorOutput "Installing dependencies..." "Yellow"
        python -m pip install --upgrade pip
        python -m pip install yara-python pefile "cmd2==2.0.0" capstone colorama pyreadline
        
        # Try to install unicorn-unipacker
        Write-ColorOutput "Installing unicorn-unipacker..." "Yellow"
        python -m pip install "unicorn-unipacker==1.0.3b7"
        
        # Install unipacker in development mode
        Write-ColorOutput "Installing unipacker..." "Yellow"
        python -m pip install -e .
        
        Set-Location ".."
        Write-ColorOutput "✓ Unipacker installation completed" "Green"
        return $true
    } catch {
        Write-ColorOutput "✗ Unipacker installation failed: $($_.Exception.Message)" "Red"
        Set-Location ".."
        return $false
    }
}

function Test-Unipacker {
    Write-ColorOutput "`n=== Testing Unipacker Installation ===" "Green"
    
    try {
        $result = python -c "import unipacker; print('Unipacker version:', unipacker.__VERSION__)" 2>&1
        if ($LASTEXITCODE -eq 0) {
            Write-ColorOutput "✓ $result" "Green"
            return $true
        } else {
            Write-ColorOutput "✗ Import test failed: $result" "Red"
            return $false
        }
    } catch {
        Write-ColorOutput "✗ Import test failed: $($_.Exception.Message)" "Red"
        return $false
    }
}

function Run-UnipackerUnpack {
    param([string]$File)
    
    Write-ColorOutput "`n=== Running Unipacker on $File ===" "Green"
    
    if (!(Test-Path $File)) {
        Write-ColorOutput "Error: File not found - $File" "Red"
        return $false
    }
    
    # Create output directory
    $outputDir = "unpacked_output"
    if (!(Test-Path $outputDir)) {
        New-Item -ItemType Directory -Path $outputDir | Out-Null
        Write-ColorOutput "Created output directory: $outputDir" "Cyan"
    }
    
    # Create unipacker script
    $scriptContent = @"
import sys
import os
sys.path.insert(0, 'unipacker-1.0.8')

from unipacker.core import UnpackerEngine
from unipacker.utils import print_cols

def unpack_file(file_path):
    print(f"[+] Starting unpacking of {file_path}")
    
    try:
        # Initialize unpacker engine
        engine = UnpackerEngine(file_path)
        
        # Set output directory
        engine.set_output_dir("unpacked_output")
        
        # Start unpacking
        print("[+] Starting emulation...")
        engine.start()
        
        print("[+] Unpacking completed!")
        return True
        
    except Exception as e:
        print(f"[-] Unpacking failed: {e}")
        return False

if __name__ == "__main__":
    if len(sys.argv) != 2:
        print("Usage: python unpack_script.py <file>")
        sys.exit(1)
    
    file_path = sys.argv[1]
    success = unpack_file(file_path)
    sys.exit(0 if success else 1)
"@

    $scriptPath = "unpack_script.py"
    $scriptContent | Out-File -FilePath $scriptPath -Encoding UTF8
    
    Write-ColorOutput "Created unpacking script: $scriptPath" "Cyan"
    
    # Run the unpacker
    try {
        Write-ColorOutput "Starting unipacker emulation..." "Yellow"
        Write-ColorOutput "This may take several minutes..." "Yellow"
        
        $result = python $scriptPath $File 2>&1
        Write-ColorOutput "Unipacker output:" "Cyan"
        Write-ColorOutput $result "White"
        
        if ($LASTEXITCODE -eq 0) {
            Write-ColorOutput "✓ Unpacking completed successfully!" "Green"
            
            # Check for output files
            if (Test-Path $outputDir) {
                $outputFiles = Get-ChildItem $outputDir
                if ($outputFiles.Count -gt 0) {
                    Write-ColorOutput "`nUnpacked files:" "Green"
                    foreach ($file in $outputFiles) {
                        Write-ColorOutput "  - $($file.Name) ($($file.Length) bytes)" "Cyan"
                    }
                } else {
                    Write-ColorOutput "No output files found in $outputDir" "Yellow"
                }
            }
            return $true
        } else {
            Write-ColorOutput "✗ Unpacking failed" "Red"
            return $false
        }
    } catch {
        Write-ColorOutput "✗ Error running unipacker: $($_.Exception.Message)" "Red"
        return $false
    }
}

function Run-InteractiveUnipacker {
    param([string]$File)
    
    Write-ColorOutput "`n=== Running Interactive Unipacker ===" "Green"
    Write-ColorOutput "This will start the interactive unipacker shell" "Yellow"
    Write-ColorOutput "Commands you can use:" "Cyan"
    Write-ColorOutput "  - load $File" "White"
    Write-ColorOutput "  - unpack" "White"
    Write-ColorOutput "  - help" "White"
    Write-ColorOutput "  - quit" "White"
    Write-ColorOutput "`nPress Enter to continue..." "Yellow"
    Read-Host
    
    try {
        python -m unipacker.shell
    } catch {
        Write-ColorOutput "Error starting interactive shell: $($_.Exception.Message)" "Red"
    }
}

# Main execution
Write-ColorOutput "=== Unipacker FSG Unpacking Tool ===" "Green"
Write-ColorOutput "Target file: $TargetFile" "Yellow"

# Step 1: Install unipacker
$installSuccess = Install-Unipacker
if (!$installSuccess) {
    Write-ColorOutput "Installation failed. Exiting." "Red"
    exit 1
}

# Step 2: Test installation
$testSuccess = Test-Unipacker
if (!$testSuccess) {
    Write-ColorOutput "Installation test failed. Exiting." "Red"
    exit 1
}

# Step 3: Run unpacker
Write-ColorOutput "`nChoose unpacking method:" "Yellow"
Write-ColorOutput "1. Automatic unpacking (recommended)" "White"
Write-ColorOutput "2. Interactive shell" "White"
$choice = Read-Host "Enter choice (1 or 2)"

switch ($choice) {
    "1" {
        $unpackSuccess = Run-UnipackerUnpack -File $TargetFile
        if ($unpackSuccess) {
            Write-ColorOutput "`n✓ Automatic unpacking completed!" "Green"
        } else {
            Write-ColorOutput "`n✗ Automatic unpacking failed. Try interactive mode." "Red"
        }
    }
    "2" {
        Run-InteractiveUnipacker -File $TargetFile
    }
    default {
        Write-ColorOutput "Invalid choice. Running automatic unpacking..." "Yellow"
        Run-UnipackerUnpack -File $TargetFile
    }
}

Write-ColorOutput "`n=== Summary ===" "Green"
Write-ColorOutput "Check the 'unpacked_output' directory for results" "Cyan"
Write-ColorOutput "If unpacking failed, try:" "Yellow"
Write-ColorOutput "1. Running in interactive mode" "White"
Write-ColorOutput "2. Using other tools like x64dbg or OllyDbg" "White"
Write-ColorOutput "3. Manual analysis with a disassembler" "White"

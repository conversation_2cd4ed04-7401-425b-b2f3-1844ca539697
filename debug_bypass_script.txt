# x64dbg 调试脚本 - 绕过断点并跳转到卡密成功
# 使用方法: 在x64dbg中执行以下命令

# 1. 清除所有断点
bc

# 2. 删除CreateFileW的断点
bp -d kernel32.CreateFileW

# 3. 运行到程序入口点
g

# 4. 查找卡密验证相关的字符串和函数
# 在内存中搜索卡密相关的字符串
find "卡密"
find "密码"
find "验证"
find "登录"
find "成功"
find "失败"

# 5. 设置内存断点来监控卡密验证过程
# 在卡密输入框出现后，设置以下断点:

# 监控字符串比较函数
bp kernel32.lstrcmpA
bp kernel32.lstrcmpW
bp msvcrt.strcmp
bp msvcrt.wcscmp

# 监控消息框函数
bp user32.MessageBoxA
bp user32.MessageBoxW

# 6. 当程序停在卡密验证处时，可以尝试以下操作:

# 方法A: 修改比较结果
# 如果停在字符串比较函数，设置EAX=0 (表示相等)
r eax=0

# 方法B: 跳过验证逻辑
# 查看当前指令，如果是条件跳转，可以修改标志位
# 例如: 如果是JNZ (不等于则跳转)，设置ZF=1
r zf=1

# 方法C: 直接跳转到成功分支
# 需要先分析代码找到成功分支的地址
# 假设成功分支在 0x401234
# jmp 0x401234

# 7. 继续执行
g

# 8. 如果需要保存修改后的程序
# 使用Scylla或其他工具dump内存

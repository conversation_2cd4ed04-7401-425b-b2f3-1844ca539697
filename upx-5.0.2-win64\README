                 ooooo     ooo  ooooooooo.  ooooooo  ooooo
                 `888'     `8'  `888   `Y88. `8888    d8'
                  888       8    888   .d88'   Y888..8P
                  888       8    888ooo88P'     `8888'
                  888       8    888           .8PY888.
                  `88.    .8'    888          d8'  `888b
                    `YbodP'     o888o       o888o  o88888o


                    The Ultimate Packer for eXecutables
   Copyright (c) 1996-2025 <PERSON>, <PERSON><PERSON><PERSON> & <PERSON>
                           https://upx.github.io



WELCOME
=======

Welcome to UPX !

UPX is a free, secure, portable, extendable, high-performance
executable packer for several executable formats.


INTRODUCTION
============

UPX is an advanced executable file compressor. UPX will typically
reduce the file size of programs and DLLs by around 50%-70%, thus
reducing disk space, network load times, download times and
other distribution and storage costs.

Programs and libraries compressed by UPX are completely self-contained
and run exactly as before, with no runtime or memory penalty for most
of the supported formats.

UPX supports a number of different executable formats, including
Windows programs and DLLs, and Linux executables.

UPX is free software distributed under the term of the GNU General
Public License. Full source code is available.

UPX may be distributed and used freely, even with commercial applications.
See the UPX License Agreements for details.


SECURITY CONTEXT
================

IMPORTANT NOTE: UPX inherits the security context of any files it handles.

This means that packing, unpacking, or even testing or listing a file requires
the same security considerations as actually executing the file.

Use UPX on trusted files only!


SHORT DOCUMENTATION
===================

'upx program.exe' will compress a program or DLL. For best compression
results try 'upx --best program.exe' or 'upx --brute program.exe'.

Please see the file UPX.DOC for the full documentation. The files
NEWS and BUGS also contain various tidbits of information.


THE FUTURE
==========

  - Stay up-to-date with ongoing OS and executable format changes

  - RISC-V 64 for Linux

  - ARM64 for Windows (help wanted)

  - We will *NOT* add any sort of protection and/or encryption.
    This only gives people a false feeling of security because
    all "protectors" can be broken by definition.

  - Fix all remaining bugs - please report any issues
    https://github.com/upx/upx/issues


COPYRIGHT
=========

Copyright (C) 1996-2025 Markus Franz Xaver Johannes Oberhumer
Copyright (C) 1996-2025 Laszlo Molnar
Copyright (C) 2000-2025 John F. Reiser

UPX is distributed with full source code under the terms of the
GNU General Public License v2+; either under the pure GPLv2+ (see
the file COPYING), or (at your option) under the GPLv+2 with special
exceptions and restrictions granting the free usage for all binaries
including commercial programs (see the file LICENSE).

This program is distributed in the hope that it will be useful,
but WITHOUT ANY WARRANTY; without even the implied warranty of
MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.

You should have received a copy of the UPX License Agreements along
with this program; see the files COPYING and LICENSE. If not,
visit the UPX home page.


Share and enjoy,
Markus & Laszlo & John


   Markus F.X.J. Oberhumer              Laszlo Molnar
   <<EMAIL>>               <<EMAIL>>

   John F. Reiser
   <<EMAIL>>


[ The term UPX is a shorthand for the Ultimate Packer for eXecutables
  and holds no connection with potential owners of registered trademarks
  or other rights. ]

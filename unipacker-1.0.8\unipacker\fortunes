Be besser!
It works on my machine
It worked yesterday
Did you ever ask yourself which packer was used to pack this shell?
This may randomly stop working
If you do not know what you are doing, stop it. If you know what you are doing, stop it as well.
Some say, there was a day when the shell worked flawlessly
Segmentation fault. (Core dumped)
Process finished with exit code 1
samuel@probook ~$
Will probably run on MS DOS
jmp 0
This unpacker is capable of everything. Well, except unpacking.
[sudo] password for samuel:
eTa WeN dIsAsSeMbLe
At least it looks nice
It works...?
No disassemble!!!
In soviet russia, un{i}packer unpacks you!
What do you want to debug today?
I love the smell of bugs in the morning.
I'm in your source securing your bits.
I did it for the pwnz.
If you send the program you are debugging to 15 friends in 143 minutes and then step three times on the same opcode you will get the name of the person who loves you.
To remove this message, put `dbxenv suppress_startup_message 7.5' in your .dbxrc
Heisenbug: A bug that disappears or alters its behavior when one attempts to probe or isolate it.
un{i}packer is for lulzhats
Microloft Visual un{i}packer.NET 2008. Now OOXML Powered!
un{i}packer is like windows 7 but even better.
We are surrounded by the enemy. - Excellent, we can attack in any direction!
un{i}packer-built farm beats the facebook one.
Thank you for using un{i}packer. Have a nice night!
Your un{i}packer was built 20h ago. TOO OLD!
Use un{i}packer! Lemons included!
Connection lost with the license server, your un{i}packer session will terminate in 30 minutes.
Wait a moment ...
Don't do this.
No such file or directory.
Default scripting languages are NodeJS and Python.
-bash: un{i}packer.py: command not found
Press any key to continue ...
Your project name should contain an uppercase letter, 8 vowels, some numbers, and the first 5 characters of your private bitcoin key.
This computer has gone to sleep.
Have you ever ordered a pizza using un{i}packer?
I thought we were friends. :_
Welcome back, lazy human!
Yo dawg!
I accidentally hacked the kernel with un{i}packer.
I endians swap.
This page intentionally left blank.
Trust no one, nor a zero. Both lie.
EIP = 0x41414141
/dev/brain: No such file or directory.
Virtual machines are great, but you lose the ability to kick the hardware.
I am Pentium of Borg. Division is futile. You will be approximated.
Don't look at the code. Don't look.
Dissasemble? No dissasemble, no dissassemble!!!!!
Warning, your trial license is about to expire.
Please register your copy of un{i}packer today! Only 29.90!
Welcome to IDA 10.0.
This software comes with no brain included. Please use your own.
rm: /: Permission denied.
In soviet Afghanistan, you unpack un{i}packer!
Wow, my cat knows un{i}packer hotkeys better than me!
Documentation is for weak people.
License server overloaded (ETOOMUCHCASH)
If you're not satisfied by our product, we'll be happy to refund you.
Already up-to-date.
SHALL WE PLAY A GAME?
One does not simply write documentation.
We are bleeding edge here. Can't you feel the razors?
There's a branch for that.
Everything up-to-date.
Sharing your latest session to Facebook ...
This should be documented, since it's not that obvious.
The Hard ROP Cafe
Wait a minute! I found a bug, self-fixing ... OK
Hold on, this should never happen!
May the segfault be with you.
I script in C, because I can.
EXPLICIT CONTENT
In Soviet Russia, un{i}packer has documentation.
Initial frame selected; you cannot go up.
Experts agree, security holes suck, and we fixed some of them!
Nothing to see here. Move along.
I accidentally un{i}packerd my filesystem today.
No fix, no sleep
You see it, you fix it!
Stop debugging me!
THIS IS NOT A BUG
Polish reversers blame git
Do you want to print 333.5K chars? (y/N)
Now with more better English!
:(){ :|:& };:
Ask not what un{i}packer can do for you - ask what you can do for un{i}packer
bash: un{i}packer.py: command not found
un{i}packer loves everyone, even Java coders, but less than others
It's not a bug, it's a work in progress
Stop swearing!
I didn't say that it was working, I said that it's implemented
Wrong argument
what happens in #un{i}packer, stays in #un{i}packer
For a full list of commands see `strings /dev/urandom`
Good morning, pal *<:-)
Of course un{i}packer runs FreeBSD
Reverser by Birth, un{i}packer by Choice
un{i}packer, what else?
This incident will be reported
command not found: calc.exe
See you at the defcon CTF
Don't waste your time
WASTED
getdruid to get eclectic uid
Too old to crash
Insert coin to continue ...
See you in shell
Log On. Hack In. Go Anywhere. Get Everything.
Are you still there?
Follow the white rabbit
Do not try to sploit that binary - that's impossible. Instead, only try to realize the truth: there is no binary.
Hello Mr. Anderson
What has been executed cannot be unexecuted
What about taking a break? Here, have this nice 0xCC.
un{i}packer is meant to be read by machines.
Prove you are a robot to continue ...
This is fine.
Taking the file and moving it somewhere else
"a collection of garbage" -- an un{i}packer pro user
A git pull a day keeps the segfault away
Are you a wizard?
un{i}packer is a great OS, but a terrible hex editor
THE CAKE IS A PIE
un{i}packer 0.9.7 is so old, my grandfarther was using it with his enigma in WWII
Have you seen the latest un{i}packer TV spot?
scp ~/.idapro/ida.key un{i}packer.org:/var/www/un{i}packer.org/pub/losers/
Too bad there is no gif support in un{i}packer. Yet. -- @un{i}packergif
Almost 5am, maybe you should go to bed.
Jingle sploits, jingle sploits, ropchain all the way.
In un{i}packerland usability is treated as a bug
un{i}packer is WYSIWYF - what you see is what you fix
Your endian swaps
*(ut64*)buffer ought to be illegal
How about Global Thermonuclear War?
There is no F5 key in un{i}packer yet
Did you know that un{i}packer is 10 years old?
un{i}packer -- leading options since 2006
Do not feed the bugs! (except delicious stacktraces)!
Feed the bugs!
You haxor! Me jane?
Now i'm like an evil engineer. MUAHAHAH
We only have bugs, features are an unintended side-effect
There are 5 minutes from WTF to FIX in un{i}packerland
Quantum dissasemble: it's there as long as you don't observe it
Buy a mac
(gdb) ^D
There's no way you could crash un{i}packer. No. Way.
TIRED OF WAITING
We fix bugs while you sleep.
You find bugs while we sleep.
Come here, we are relatively friendly
You need some new glasses
Mind the tab
Buy a Mac
What is the most complex un{i}packer command? q - then you have to deal with real life /o\
If you're having fun using un{i}packer, odds are that you're doing something wrong.
Don't trust what can't be compiled
Coffee time!
Add more blockchains to your life.
Congratulations! You got the segfault 1.000.000! Click [here] to win a prize!
Well, it looks like it's working.
There's more than one way to skin a cat
git pull now
git checkout hamster
This is an unregistered copy.
Place a cat on your keyboard while running un{i}packer, you'll not believe what will happen next
This binary may contain traces of human
Help subcommand will be eventually removed.
Your mouse has moved. un{i}packer NT must be restarted for the change to take effect. Reboot now? [ OK ]
There is only one binary, and we are all just reversing pieces of it.
un{i}packer is like violence. If it doesn't solve your problem, you aren't using enough.
Order pizza for $12.48? [Y/n]
un{i}packerOS un{i}packerpad 0.1 SMP GENERIC un{i}packer_64 GNU/un{i}packerOS
AHHHHH!!!! ASSEMBLY CODE!!!!!! HOLD ME I'M SCARED!!!!!!!!!!
In un{i}packer we trust
We don't make mistakes... just happy little segfaults.
Use headphones for best experience.
Starting bitcoin miner in background...
Downloading and verifying the blockchain...
The anti-virus database has been updated.
The motion picture contained in this videodisc is protected under the copyright laws.
This software is sold for home use only and all other rights are expressly reversed by the copyleft owner.
Any commercial use or duplication of this copylefted material without prior licensing is forbidden.
Violators will be prosecuted.
This shell has been seized by the Internet's Police.
Error: There's a missing space before the opening parenthesis '('
WARNING: r_list_length: assertion 'list' failed (line 55)
This accessory is not supported by this version of un{i}packer.
Sorry, un{i}packer has experienced an internal error.
Segmentation fault (core dumped)

param(
    [Parameter(Mandatory=$true)]
    [string]$FilePath
)

function Analyze-PEFile {
    param([string]$Path)
    
    Write-Host "=== PE File Analysis ===" -ForegroundColor Green
    Write-Host "File: $Path" -ForegroundColor Yellow

    # Get basic file info
    $fileInfo = Get-ItemProperty $Path
    Write-Host "File Size: $($fileInfo.Length) bytes" -ForegroundColor Cyan
    Write-Host "Created: $($fileInfo.CreationTime)" -ForegroundColor Cyan
    Write-Host "Modified: $($fileInfo.LastWriteTime)" -ForegroundColor Cyan
    
    # Read file header
    $bytes = Get-Content $Path -Encoding Byte -TotalCount 2048

    # Check DOS header
    if ($bytes[0] -eq 0x4D -and $bytes[1] -eq 0x5A) {
        Write-Host "Valid PE file (MZ header)" -ForegroundColor Green
    } else {
        Write-Host "Invalid PE file" -ForegroundColor Red
        return
    }

    # Get PE header offset
    $peOffset = [BitConverter]::ToUInt32($bytes, 60)
    Write-Host "PE Header Offset: 0x$($peOffset.ToString('X'))" -ForegroundColor Cyan

    # Check PE signature
    if ($bytes[$peOffset] -eq 0x50 -and $bytes[$peOffset+1] -eq 0x45) {
        Write-Host "Valid PE signature" -ForegroundColor Green
    }

    # Get section table info
    $numberOfSections = [BitConverter]::ToUInt16($bytes, $peOffset + 6)
    Write-Host "Number of sections: $numberOfSections" -ForegroundColor Cyan
    
    # Calculate section table start position
    $optionalHeaderSize = [BitConverter]::ToUInt16($bytes, $peOffset + 20)
    $sectionTableOffset = $peOffset + 24 + $optionalHeaderSize

    Write-Host "`n=== Section Table Info ===" -ForegroundColor Green

    for ($i = 0; $i -lt $numberOfSections; $i++) {
        $sectionOffset = $sectionTableOffset + ($i * 40)

        # Read section name (8 bytes)
        $nameBytes = $bytes[$sectionOffset..($sectionOffset + 7)]
        $sectionName = [System.Text.Encoding]::ASCII.GetString($nameBytes).TrimEnd([char]0)

        # Read virtual size and virtual address
        $virtualSize = [BitConverter]::ToUInt32($bytes, $sectionOffset + 8)
        $virtualAddress = [BitConverter]::ToUInt32($bytes, $sectionOffset + 12)
        $rawSize = [BitConverter]::ToUInt32($bytes, $sectionOffset + 16)
        $rawAddress = [BitConverter]::ToUInt32($bytes, $sectionOffset + 20)

        Write-Host "Section $($i+1): $sectionName" -ForegroundColor Yellow
        Write-Host "  Virtual Size: 0x$($virtualSize.ToString('X'))" -ForegroundColor White
        Write-Host "  Virtual Address: 0x$($virtualAddress.ToString('X'))" -ForegroundColor White
        Write-Host "  Raw Size: 0x$($rawSize.ToString('X'))" -ForegroundColor White
        Write-Host "  Raw Address: 0x$($rawAddress.ToString('X'))" -ForegroundColor White
    }
    
    # Check common packer signatures
    Write-Host "`n=== Packer Detection ===" -ForegroundColor Green
    
    $fileContent = Get-Content $Path -Raw -Encoding Byte
    $stringContent = [System.Text.Encoding]::ASCII.GetString($fileContent)
    
    $packers = @{
        "UPX" = @("UPX!", "UPX0", "UPX1", "UPX2")
        "ASPack" = @("ASPack", "aPLib")
        "PECompact" = @("PECompact", "pec1", "pec2")
        "Petite" = @("petite", ".petite")
        "NSPack" = @("NSPack", ".nsp")
        "FSG" = @("FSG!", "FSG")
        "MEW" = @("MEW", ".MEW")
        "Themida" = @("Themida", "WinLicense")
        "VMProtect" = @("VMProtect", ".vmp")
        "Enigma" = @("Enigma", ".enigma")
        "Obsidium" = @("Obsidium", ".obsidium")
        "WinUpack" = @("WinUpack", ".winup")
        "RLPack" = @("RLPack", ".RLPack")
        "Exe32Pack" = @("Exe32Pack", ".exe32p")
        "MoleBox" = @("MoleBox", ".molebox")
        "Armadillo" = @("Armadillo", ".arma")
    }
    
    $detectedPackers = @()
    
    foreach ($packer in $packers.Keys) {
        foreach ($signature in $packers[$packer]) {
            if ($stringContent -match [regex]::Escape($signature)) {
                $detectedPackers += $packer
                Write-Host "Detected: $packer (signature: $signature)" -ForegroundColor Red
                break
            }
        }
    }

    if ($detectedPackers.Count -eq 0) {
        Write-Host "No common packer signatures detected" -ForegroundColor Green
        Write-Host "  This might be an unpacked native PE file" -ForegroundColor Yellow
    }

    # Check entry point
    $entryPointRVA = [BitConverter]::ToUInt32($bytes, $peOffset + 40)
    Write-Host "`nEntry Point RVA: 0x$($entryPointRVA.ToString('X'))" -ForegroundColor Cyan

    # Check import table
    $importTableRVA = [BitConverter]::ToUInt32($bytes, $peOffset + 128)
    if ($importTableRVA -ne 0) {
        Write-Host "Import Table RVA: 0x$($importTableRVA.ToString('X'))" -ForegroundColor Cyan
    } else {
        Write-Host "Warning: No import table - might be packed" -ForegroundColor Yellow
    }
}

# Execute analysis
if (Test-Path $FilePath) {
    Analyze-PEFile -Path $FilePath
} else {
    Write-Host "Error: File not found - $FilePath" -ForegroundColor Red
}

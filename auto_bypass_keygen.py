#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
自动化卡密绕过脚本
用于自动处理程序调试过程中的断点问题并跳转到卡密成功逻辑
"""

import subprocess
import time
import os
import sys
from pathlib import Path

class DebugBypass:
    def __init__(self, target_exe):
        self.target_exe = target_exe
        self.x64dbg_path = self.find_x64dbg()
        
    def find_x64dbg(self):
        """查找x64dbg安装路径"""
        common_paths = [
            r"C:\Program Files\x64dbg\x64dbg.exe",
            r"C:\Program Files (x86)\x64dbg\x64dbg.exe",
            r"C:\x64dbg\x64dbg.exe",
            r".\x64dbg\x64dbg.exe"
        ]
        
        for path in common_paths:
            if os.path.exists(path):
                return path
        
        print("[-] 未找到x64dbg，请手动指定路径")
        return None
    
    def create_debug_script(self):
        """创建调试脚本"""
        script_content = """
// 自动化卡密绕过脚本
log "开始自动化调试..."

// 清除所有断点
bc

// 运行程序
run

// 等待程序加载完成
sleep 2000

// 设置API监控断点
log "设置API监控断点..."
bp user32.MessageBoxA
bp user32.MessageBoxW
bp kernel32.lstrcmpA
bp kernel32.lstrcmpW

// 继续执行直到卡密验证
log "等待卡密验证..."
g

// 脚本将在断点处暂停，此时需要手动干预
"""
        
        script_path = "auto_bypass.txt"
        with open(script_path, 'w', encoding='utf-8') as f:
            f.write(script_content)
        
        return script_path
    
    def run_debug_session(self):
        """运行调试会话"""
        if not self.x64dbg_path:
            print("[-] 无法启动x64dbg")
            return False
        
        script_path = self.create_debug_script()
        
        try:
            # 启动x64dbg
            cmd = [self.x64dbg_path, self.target_exe]
            print(f"[+] 启动调试器: {' '.join(cmd)}")
            
            process = subprocess.Popen(cmd)
            
            print("[+] 调试器已启动")
            print("[!] 请在x64dbg中执行以下步骤:")
            print("    1. 加载程序后，执行脚本: auto_bypass.txt")
            print("    2. 当程序停在API断点时，检查调用栈")
            print("    3. 如果是卡密验证相关，修改返回值或跳转")
            print("    4. 使用 'r eax=0' 设置比较结果为相等")
            print("    5. 使用 'g' 继续执行")
            
            return True
            
        except Exception as e:
            print(f"[-] 启动调试器失败: {e}")
            return False
    
    def analyze_memory_patterns(self):
        """分析内存模式以找到卡密验证逻辑"""
        print("[+] 分析程序内存模式...")
        
        # 常见的卡密验证特征
        patterns = [
            "卡密错误",
            "验证失败", 
            "登录成功",
            "密码正确",
            "授权成功",
            "License",
            "Serial",
            "Key"
        ]
        
        print("[+] 建议在调试器中搜索以下字符串:")
        for pattern in patterns:
            print(f"    - {pattern}")
        
        print("\n[+] 常见的绕过方法:")
        print("    1. 在字符串比较函数处修改返回值")
        print("    2. 在条件跳转处修改标志位")
        print("    3. 直接跳转到成功分支")
        print("    4. 修改内存中的验证标志")

def main():
    if len(sys.argv) != 2:
        print("用法: python auto_bypass_keygen.py <程序路径>")
        print("示例: python auto_bypass_keygen.py 程序2O.exe")
        return
    
    target_exe = sys.argv[1]
    
    if not os.path.exists(target_exe):
        print(f"[-] 文件不存在: {target_exe}")
        return
    
    print(f"[+] 目标程序: {target_exe}")
    
    bypass = DebugBypass(target_exe)
    
    # 分析内存模式
    bypass.analyze_memory_patterns()
    
    # 运行调试会话
    bypass.run_debug_session()

if __name__ == "__main__":
    main()

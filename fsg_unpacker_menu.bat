@echo off
cls
echo ================================================================
echo                    FSG Unpacker Toolkit
echo ================================================================
echo.
echo Target file: GSEEGB.exe
echo Detected packer: FSG (Fast Small Good)
echo File size: 7,824,384 bytes
echo Entry point: 0x75C100
echo.
echo ================================================================
echo                    Available Methods
echo ================================================================
echo.
echo [1] Automatic unpacking attempts
echo     - UPX unpacking (tested - failed)
echo     - Unipacker (tested - failed, PE header issues)
echo.
echo [2] Manual unpacking tools (recommended)
echo     - x64dbg: https://x64dbg.com/
echo     - OllyDbg: http://www.ollydbg.de/
echo     - PEiD + FSG plugin
echo.
echo [3] Online unpacking services
echo     - VirusTotal
echo     - Hybrid Analysis
echo     - Any.run
echo.
echo [4] Generated helper files
if exist "analyze_packer.ps1" echo     * analyze_packer.ps1 - PE file analysis script
if exist "debug_config.txt" echo     * debug_config.txt - Debugger configuration
if exist "fsg_unpacking_guide.md" echo     * fsg_unpacking_guide.md - Detailed unpacking guide
if exist "run_unipacker_simple.py" echo     * run_unipacker_simple.py - Unipacker runner script
echo.
echo ================================================================
echo                    Quick Actions
echo ================================================================
echo.
echo Choose an action:
echo [A] Run PE file analysis
echo [B] Try Python Unipacker
echo [C] View debugger config
echo [D] Open unpacking guide
echo [E] Exit
echo.
set /p choice="Enter your choice (A/B/C/D/E): "

if /i "%choice%"=="A" goto analyze
if /i "%choice%"=="B" goto unipacker
if /i "%choice%"=="C" goto config
if /i "%choice%"=="D" goto guide
if /i "%choice%"=="E" goto end
echo Invalid choice. Please try again.
pause
goto start

:analyze
echo.
echo Running PE file analysis...
if exist "analyze_packer.ps1" (
    powershell -ExecutionPolicy Bypass -File analyze_packer.ps1 -FilePath "GSEEGB.exe"
) else (
    echo Error: analyze_packer.ps1 not found
)
echo.
pause
goto start

:unipacker
echo.
echo Trying Unipacker...
if exist "run_unipacker_simple.py" (
    python run_unipacker_simple.py
) else (
    echo Error: run_unipacker_simple.py not found
)
echo.
pause
goto start

:config
echo.
echo Debugger Configuration:
echo ================================
if exist "debug_config.txt" (
    type debug_config.txt
) else (
    echo Error: debug_config.txt not found
)
echo ================================
echo.
pause
goto start

:guide
echo.
echo Opening unpacking guide...
if exist "fsg_unpacking_guide.md" (
    start notepad "fsg_unpacking_guide.md"
) else (
    echo Error: fsg_unpacking_guide.md not found
)
echo.
pause
goto start

:start
cls
goto menu

:menu
echo ================================================================
echo                    FSG Unpacker Toolkit
echo ================================================================
echo.
echo Target file: GSEEGB.exe
echo Detected packer: FSG (Fast Small Good)
echo File size: 7,824,384 bytes
echo Entry point: 0x75C100
echo.
echo ================================================================
echo                    Quick Actions
echo ================================================================
echo.
echo Choose an action:
echo [A] Run PE file analysis
echo [B] Try Python Unipacker
echo [C] View debugger config
echo [D] Open unpacking guide
echo [E] Exit
echo.
set /p choice="Enter your choice (A/B/C/D/E): "

if /i "%choice%"=="A" goto analyze
if /i "%choice%"=="B" goto unipacker
if /i "%choice%"=="C" goto config
if /i "%choice%"=="D" goto guide
if /i "%choice%"=="E" goto end
echo Invalid choice. Please try again.
pause
goto start

:end
cls
echo ================================================================
echo                        Summary
echo ================================================================
echo.
echo Automatic unpacking result: FAILED (FSG modified PE headers)
echo.
echo Recommended next steps:
echo 1. Use x64dbg for manual unpacking (most recommended)
echo 2. Try OllyDbg with manual analysis
echo 3. Look for specialized FSG unpacking tools
echo 4. Use online unpacking services (check security first)
echo.
echo Manual unpacking key steps:
echo 1. Load GSEEGB.exe in debugger
echo 2. Set breakpoint at entry point 0x75C100
echo 3. Step through to find Original Entry Point (OEP)
echo 4. Dump memory at OEP
echo 5. Fix imports and relocations
echo.
echo For detailed information, see: fsg_unpacking_guide.md
echo.
echo Thank you for using FSG Unpacker Toolkit!
echo ================================================================
pause
